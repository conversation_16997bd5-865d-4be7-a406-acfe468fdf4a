import React, { useEffect, useRef, useState, useCallback } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { Box, Typography } from "@mui/material";
import HTMLFlipBook from "react-pageflip";
import WordHighlightOverlay from "./WordHighlightOverlay";
import type { TextItem, HighlightArea, PDFDocument } from "../types";

interface PDFViewerProps {
  document: PDFDocument | null;
  highlights: HighlightArea[];
  onPageChange: (pageNumber: number) => void;
  onTextSelection: (text: string, pageIndex: number) => void;
  currentHighlight: TextItem | null;
  pageHeight: number;
  isReading: boolean;
  textItems: TextItem[];
  currentHighlightedWord: TextItem | null;
  onWordClick: (word: string, textItem: TextItem) => void;
  onSentenceClick: (
    sentence: string,
    startIndex: number,
    pageIndex: number,
  ) => void;
  onParagraphClick: (
    paragraph: string,
    startIndex: number,
    pageIndex: number,
  ) => void;
  onWordHover: (word: string, textItem: TextItem) => void;
  onSentenceHover: (
    sentence: string,
    startIndex: number,
    pageIndex: number,
  ) => void;
  onParagraphHover: (
    paragraph: string,
    startIndex: number,
    pageIndex: number,
  ) => void;
  speaking: boolean;
  paused: boolean;
  isStoppedAndReady: boolean;
  readWords: Set<string>;
  highlightMode?: boolean;
  leftPage: number;
  rightPage: number | null;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  document,
  highlights,
  onPageChange,
  onTextSelection,
  currentHighlight,
  pageHeight,
  isReading,
  textItems,
  currentHighlightedWord,
  onWordClick,
  onSentenceClick,
  onParagraphClick,
  onWordHover,
  onSentenceHover,
  onParagraphHover,
  speaking,
  paused,
  isStoppedAndReady,
  readWords,
  highlightMode = false,
  leftPage,
  rightPage,
}) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [scale, setScale] = useState(1);
  const [actualPageDimensions, setActualPageDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);
  const [isFlipBookReady, setIsFlipBookReady] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const flipBookRef = useRef<any>(null);

  useEffect(() => {
    pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  }, []);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const onPageLoadSuccess = useCallback(
    (page: any) => {
      const viewport = page.getViewport({ scale: 1 });
      const baseWidth = viewport.width;
      const baseHeight = viewport.height;

      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        const containerHeight = containerRef.current.offsetHeight;
        const isCoverPage = document?.currentPage === 1;

        const scaleByWidth =
          containerWidth / (isCoverPage ? baseWidth : baseWidth * 2);
        const scaleByHeight = containerHeight / baseHeight;
        const newScale = Math.min(scaleByWidth, scaleByHeight, 1.5);

        const scaledViewport = page.getViewport({ scale: newScale });

        setActualPageDimensions({
          width: scaledViewport.width,
          height: scaledViewport.height,
        });
        setScale(Math.round(newScale * 1000) / 1000);
      }
    },
    [document?.currentPage],
  );

  const calculatePreciseScale = useCallback(() => {
    if (!containerRef.current || !document?.pdfDoc) return;

    document.pdfDoc
      .getPage(document.currentPage)
      .then((page) => {
        onPageLoadSuccess(page);
      })
      .catch((error) => {
        console.error("Error loading page for scale calculation:", error);
      });
  }, [document?.pdfDoc, document?.currentPage, onPageLoadSuccess]);

  useEffect(() => {
    const handleResize = () => {
      setTimeout(calculatePreciseScale, 100);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [calculatePreciseScale]);

  const handleTextSelection = (event: any) => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      const text = selection.toString();
      const pageIndex = document?.currentPage ? document.currentPage - 1 : 0;
      onTextSelection(text, pageIndex);
      selection.removeAllRanges();
    }
  };

  // Synchronize flipbook page with currentPage
  useEffect(() => {
    if (isFlipBookReady && flipBookRef.current && numPages && document) {
      let flipPage;
      if (document.currentPage === 1) {
        flipPage = 0;
      } else {
        flipPage = Math.ceil((document.currentPage - 1) / 2);
      }
      const flipBookInstance = flipBookRef.current.pageFlip();
      if (flipBookInstance) {
        flipBookInstance.flip(flipPage, "top");
      }
    }
  }, [document?.currentPage, isFlipBookReady, numPages, document]);

  if (!document || !document.url) {
    return <Box>Loading PDF...</Box>;
  }

  return (
    <Box
      ref={containerRef}
      sx={{
        position: "relative",
        height: "100%",
        overflow: "auto",
      }}
      onMouseUp={handleTextSelection}
    >
      <Box
        sx={{
          position: "absolute",
          bottom: "16px",
          left: "50%",
          transform: "translateX(-50%)",
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          padding: "8px 16px",
          borderRadius: "20px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          zIndex: 50,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            fontWeight: 500,
            color: "#333",
            userSelect: "none",
          }}
        >
          Page{" "}
          {currentHighlightedWord && isReading
            ? currentHighlightedWord.pageIndex + 1
            : document.currentPage}{" "}
          of {numPages || 0}
        </Typography>
      </Box>

      {highlightMode && (
        <Box
          sx={{
            position: "absolute",
            top: "16px",
            left: "50%",
            transform: "translateX(-50%)",
            backgroundColor: "rgba(25, 118, 210, 0.9)",
            color: "white",
            padding: "8px 16px",
            borderRadius: "20px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
            zIndex: 50,
            "@keyframes fadeInPulse": {
              "0%": {
                opacity: 0,
                transform: "translateX(-50%) scale(0.95)",
              },
              "50%": {
                opacity: 0.8,
                transform: "translateX(-50%) scale(1.02)",
              },
              "100%": {
                opacity: 1,
                transform: "translateX(-50%) scale(1)",
              },
            },
            animation: "fadeInPulse 0.5s ease-in-out",
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              userSelect: "none",
              fontSize: "0.875rem",
            }}
          >
            🎯 Highlight Mode Active - Select text to read
          </Typography>
        </Box>
      )}
      <Document file={document.url} onLoadSuccess={onDocumentLoadSuccess}>
        <HTMLFlipBook
          width={actualPageDimensions?.width || 500}
          height={actualPageDimensions?.height || 700}
          showCover={true}
          flippingTime={600}
          style={{ margin: "0 auto" }}
          ref={flipBookRef}
          onInit={() => setIsFlipBookReady(true)}
          onFlip={(e) => {
            if (highlightMode) {
              // Disable page flipping when highlight mode is active
              return;
            }
            const flipPage = e.data;
            let newCurrentPage;
            if (flipPage === 0) {
              newCurrentPage = 1;
            } else {
              newCurrentPage = (flipPage - 1) * 2 + 2;
            }
            onPageChange(newCurrentPage);
          }}
        >
          {Array.from({ length: numPages || 0 }, (_, index) => (
            <div key={index}>
              <Box sx={{ position: "relative" }}>
                <Page
                  pageNumber={index + 1}
                  scale={scale}
                  renderTextLayer={false}
                  renderAnnotationLayer={false}
                  onLoadSuccess={index === 0 ? onPageLoadSuccess : undefined}
                />
                <WordHighlightOverlay
                  textItems={textItems.filter(
                    (item) => item.pageIndex === index,
                  )}
                  pageNumber={index + 1}
                  scale={scale}
                  pageHeight={actualPageDimensions?.height || pageHeight}
                  actualPageDimensions={actualPageDimensions}
                  currentHighlightedWord={currentHighlightedWord}
                  onWordClick={onWordClick}
                  onSentenceClick={onSentenceClick}
                  onParagraphClick={onParagraphClick}
                  onWordHover={onWordHover}
                  onSentenceHover={onSentenceHover}
                  onParagraphHover={onParagraphHover}
                  isReading={isReading}
                  speaking={speaking}
                  paused={paused}
                  readWords={readWords}
                />
              </Box>
            </div>
          ))}
        </HTMLFlipBook>
      </Document>
    </Box>
  );
};

export default PDFViewer;
