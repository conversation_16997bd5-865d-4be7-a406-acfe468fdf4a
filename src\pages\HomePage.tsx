import React, { useState, useEffect, useCallback, useRef } from "react";
import { Container, Box, Paper, Typography, Button } from "@mui/material";
import { SkipPrevious, PlayArrow, SkipNext, Stop } from "@mui/icons-material";
import PDFViewer from "../components/PDFViewer";
import AudioControls from "../components/AudioControls";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { usePDFDocument } from "../hooks/usePDFDocument";
import { useSpeechSynthesis } from "../hooks/useSpeechSynthesis";
import type { TextItem } from "../types";
import { extractTextFromPage } from "../utils/pdfHelpers";
import { PageTitle } from "../components/page-title-underline";
import { Mail, Phone, MapPin, Clock } from "lucide-react";
import type { VoiceOptions } from "../types";

const HomePage: React.FC = () => {
  const {
    documentInfo,
    highlights,
    loadDocument,
    setCurrentPage,
    addHighlight,
    clearHighlights,
    isLoading,
  } = usePDFDocument();

  const {
    voices,
    options: speechOptions,
    speaking,
    paused,
    speak,
    pause,
    resume,
    stop: stopSpeech,
    setOptions: setSpeechOptions,
    speakFromPosition,
  } = useSpeechSynthesis();

  const [currentHighlightedText, setCurrentHighlightedText] = useState("");
  const [currentHighlight, setCurrentHighlight] = useState<TextItem | null>(null);
  const [currentHighlightedWord, setCurrentHighlightedWord] = useState<TextItem | null>(null);
  const [spreadTextItems, setSpreadTextItems] = useState<TextItem[]>([]);
  const [isAutoReading, setIsAutoReading] = useState(false);
  const [isStoppedAndReady, setIsStoppedAndReady] = useState(false);
  const [readWords, setReadWords] = useState<Set<string>>(new Set());
  const [highlightMode, setHighlightMode] = useState(false);
  const [hoveredReadingPosition, setHoveredReadingPosition] = useState<{
    text: string;
    startIndex: number;
    type: "word" | "sentence" | "paragraph";
    textItem?: TextItem;
  } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [leftPage, setLeftPage] = useState<number>(1);
  const [rightPage, setRightPage] = useState<number | null>(null);

  useEffect(() => {
    loadDocument("/src/assets/sample.pdf");
  }, [loadDocument]);

  const getPageTextAndItems = useCallback(
    async (pageNumber: number) => {
      if (!documentInfo || !documentInfo.pdfDoc)
        return { text: "", textItems: [], pageIndex: pageNumber };

      const { fullText, textItems: pageTextItems } = await extractTextFromPage(
        documentInfo.pdfDoc,
        pageNumber
      );

      return { text: fullText.trim(), textItems: pageTextItems, pageIndex: pageNumber };
    },
    [documentInfo]
  );

  const getSpreadTextAndItems = useCallback(
    async (leftPage: number, rightPage: number | null) => {
      let combinedText = "";
      let combinedTextItems: TextItem[] = [];

      const leftResult = await getPageTextAndItems(leftPage);
      if (leftResult.text) {
        combinedText += leftResult.text;
        combinedTextItems = [...combinedTextItems, ...leftResult.textItems.map(item => ({
          ...item,
          pageIndex: leftPage - 1
        }))];
      }

      if (rightPage && rightPage <= (documentInfo?.totalPages || 0)) {
        const rightResult = await getPageTextAndItems(rightPage);
        if (rightResult.text) {
          combinedText += combinedText ? ` ${rightResult.text}` : rightResult.text;
          combinedTextItems = [...combinedTextItems, ...rightResult.textItems.map(item => ({
            ...item,
            pageIndex: rightPage - 1
          }))];
        }
      }

      console.log(`Spread ${leftPage}-${rightPage}:`, { text: combinedText, textItems: combinedTextItems });

      return { text: combinedText, textItems: combinedTextItems };
    },
    [getPageTextAndItems, documentInfo]
  );

  const handleTextSelection = useCallback(
    async (text: string, pageIndex: number) => {
      if (!text || !documentInfo || !documentInfo.pdfDoc) return;

      // Only allow text selection in highlight mode or when not speaking
      if (!highlightMode && speaking && !paused) return;

      if (speaking) {
        stopSpeech();
      }
      setIsAutoReading(false);
      setIsStoppedAndReady(false);

      const { text: fullText, textItems } = await getPageTextAndItems(pageIndex + 1);

      const selectedTextIndex = fullText.indexOf(text);
      if (selectedTextIndex === -1) {
        setCurrentHighlightedText(text);
        addHighlight({ pageIndex, text });
        speak(
          text,
          [],
          pageIndex,
          (event) => {
            if (
              event.charIndex !== undefined &&
              event.charLength !== undefined
            ) {
              const charIndex = event.charIndex;
              const currentTextItem = textItems.find(
                (item) =>
                  charIndex >= item.startIndex && charIndex < item.endIndex
              );
              if (currentTextItem) {
                setCurrentHighlightedWord(currentTextItem);
              }
            }
          },
          () => {
            setCurrentHighlightedWord(null);
          }
        );
        return;
      }

      setCurrentHighlightedText(text);
      addHighlight({ pageIndex, text });
      speakFromPosition(
        fullText,
        selectedTextIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
    },
    [
      documentInfo,
      speaking,
      paused,
      highlightMode,
      stopSpeech,
      getPageTextAndItems,
      speakFromPosition,
      addHighlight,
      speak,
    ]
  );

  const handleWordClick = useCallback(
    async (word: string, textItem: TextItem) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;
      if (!highlightMode && speaking && !paused) return;

      if (speaking) {
        stopSpeech();
      }
      setIsAutoReading(false);
      setIsStoppedAndReady(false);

      const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

      const wordStartIndex = textItem.startIndex;

      speakFromPosition(
        text,
        wordStartIndex,
        [],
        textItem.pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
    },
    [documentInfo, speaking, paused, highlightMode, stopSpeech, getPageTextAndItems, speakFromPosition]
  );

  const handleSentenceClick = useCallback(
    async (sentence: string, startIndex: number, pageIndex: number) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;
      if (!highlightMode && speaking && !paused) return;

      if (speaking) {
        stopSpeech();
      }
      setIsAutoReading(false);
      setIsStoppedAndReady(false);

      const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

      speakFromPosition(
        text,
        startIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
    },
    [documentInfo, speaking, paused, highlightMode, stopSpeech, getPageTextAndItems, speakFromPosition]
  );

  const handleParagraphClick = useCallback(
    async (paragraph: string, startIndex: number, pageIndex: number) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;
      if (!highlightMode && speaking && !paused) return;

      if (speaking) {
        stopSpeech();
      }
      setIsAutoReading(false);
      setIsStoppedAndReady(false);

      const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

      speakFromPosition(
        text,
        startIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
        }
      );
    },
    [documentInfo, speaking, paused, highlightMode, stopSpeech, getPageTextAndItems, speakFromPosition]
  );

  const handleWordHover = useCallback(
    async (word: string, textItem: TextItem) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      if (textItem) {
        setHoveredReadingPosition({
          text: word,
          startIndex: textItem.startIndex,
          type: "word",
          textItem: textItem,
        });
      } else {
        setHoveredReadingPosition(null);
      }

      // Only allow hover reading in highlight mode
      if (!highlightMode) return;
      const canHover = !speaking || paused;
      if (!canHover) return;

      const { text, textItems } = await getPageTextAndItems(textItem.pageIndex + 1);

      const wordStartIndex = textItem.startIndex;

      setIsStoppedAndReady(false);
      speakFromPosition(
        text,
        wordStartIndex,
        [],
        textItem.pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
          if (!speaking) {
            setIsStoppedAndReady(true);
          }
        }
      );
    },
    [documentInfo, speaking, paused, highlightMode, getPageTextAndItems, speakFromPosition]
  );

  const handleSentenceHover = useCallback(
    async (sentence: string, startIndex: number, pageIndex: number) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      // Only allow hover reading in highlight mode
      if (!highlightMode) return;
      const canHover = !speaking || paused;
      if (!canHover) return;

      const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

      setIsStoppedAndReady(false);
      speakFromPosition(
        text,
        startIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
          if (!speaking) {
            setIsStoppedAndReady(true);
          }
        }
      );
    },
    [documentInfo, speaking, paused, highlightMode, getPageTextAndItems, speakFromPosition]
  );

  const handleParagraphHover = useCallback(
    async (paragraph: string, startIndex: number, pageIndex: number) => {
      if (!documentInfo || !documentInfo.pdfDoc) return;

      // Only allow hover reading in highlight mode
      if (!highlightMode) return;
      const canHover = !speaking || paused;
      if (!canHover) return;

      const { text, textItems } = await getPageTextAndItems(pageIndex + 1);

      setIsStoppedAndReady(false);
      speakFromPosition(
        text,
        startIndex,
        [],
        pageIndex,
        (event) => {
          if (event.charIndex !== undefined && event.charLength !== undefined) {
            const charIndex = event.charIndex;
            const currentTextItem = textItems.find(
              (item) =>
                charIndex >= item.startIndex && charIndex < item.endIndex
            );
            if (currentTextItem) {
              setCurrentHighlightedWord(currentTextItem);
            }
          }
        },
        () => {
          setCurrentHighlightedWord(null);
          if (!speaking) {
            setIsStoppedAndReady(true);
          }
        }
      );
    },
    [documentInfo, speaking, paused, highlightMode, getPageTextAndItems, speakFromPosition]
  );

  const handleReadingComplete = useCallback(async () => {
    if (!documentInfo || !isAutoReading) return;

    const currentPage = documentInfo.currentPage;
    const isCoverPage = currentPage === 1;
    
    // Always go to next page (cover: 1->2, spreads: n->n+2)
    let nextPage = isCoverPage ? 2 : currentPage + 2;

    if (nextPage > documentInfo.totalPages) {
      setIsAutoReading(false);
      stopSpeech();
      clearHighlights();
      setCurrentHighlight(null);
      setCurrentHighlightedWord(null);
      setReadWords(new Set());
      setIsStoppedAndReady(true);
      console.log("Document reading complete");
    } else {
      stopSpeech();
      setReadWords(new Set());
      await setCurrentPage(nextPage);
      setIsAutoReading(true);
      console.log(`Moving to next spread starting with page ${nextPage}`);
    }
  }, [documentInfo, isAutoReading, setCurrentPage, stopSpeech, clearHighlights]);

  useEffect(() => {
    if (
      !isAutoReading ||
      !documentInfo ||
      speaking ||
      isLoading ||
      !documentInfo.pdfDoc
    )
      return;

    const readingTimeout = setTimeout(async () => {
      const currentPage = documentInfo.currentPage;
      const isCoverPage = currentPage === 1;
      
      const leftPage = isCoverPage ? currentPage : currentPage % 2 === 0 ? currentPage : currentPage - 1;
      const rightPage = isCoverPage ? null : leftPage + 1 <= documentInfo.totalPages ? leftPage + 1 : null;

      console.log(`Current spread: ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);

      const { text, textItems } = await getSpreadTextAndItems(leftPage, rightPage);

      setSpreadTextItems(textItems);
      setCurrentHighlightedText(text);

      const leftPageItems = textItems.filter(item => item.pageIndex === leftPage - 1);
      const rightPageItems = rightPage ? textItems.filter(item => item.pageIndex === rightPage - 1) : [];

      const readPage = async (pageText: string, pageItems: TextItem[], pageIndex: number) => {
        if (!pageText) {
          console.log(`No text on page ${pageIndex + 1}, skipping`);
          return;
        }
        
        console.log(`Reading page ${pageIndex + 1}`);
        await new Promise<void>((resolve) => {
          speak(
            pageText,
            [],
            pageIndex,
            (event) => {
              if (event.charIndex !== undefined && event.charLength !== undefined) {
                const charIndex = event.charIndex;
                const textItem = pageItems.find(
                  (item) =>
                    charIndex >= item.startIndex && charIndex < item.endIndex
                );
                if (textItem) {
                  const word = pageText
                    .substring(charIndex, charIndex + event.charLength)
                    .trim();

                  const wordStart = Math.max(charIndex, textItem.startIndex);
                  const wordEnd = Math.min(
                    charIndex + event.charLength,
                    textItem.endIndex
                  );

                  const textItemLength = Math.max(
                    textItem.endIndex - textItem.startIndex,
                    1
                  );
                  const wordLength = Math.max(wordEnd - wordStart, 1);

                  const wordProgress =
                    (wordStart - textItem.startIndex) / textItemLength;
                  const wordLengthRatio = wordLength / textItemLength;

                  const clampedProgress = Math.max(0, Math.min(1, wordProgress));
                  const clampedLengthRatio = Math.max(
                    0.1,
                    Math.min(1, wordLengthRatio)
                  );

                  const preciseX =
                    textItem.coordinates.x +
                    textItem.coordinates.width * clampedProgress;
                  const preciseWidth = Math.max(
                    textItem.coordinates.width * clampedLengthRatio,
                    15
                  );

                  const highlightItem = {
                    ...textItem,
                    text: word,
                    startIndex: wordStart,
                    endIndex: wordEnd,
                    coordinates: {
                      ...textItem.coordinates,
                      x: preciseX,
                      width: preciseWidth,
                      height: textItem.coordinates.height + 4,
                    },
                  };

                  setCurrentHighlight(highlightItem);
                  setCurrentHighlightedWord(textItem);

                  setReadWords((prev) =>
                    new Set(prev).add(
                      `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`
                    )
                  );
                }
              }
            },
            () => {
              setCurrentHighlight(null);
              setCurrentHighlightedWord(null);
              resolve();
            }
          );
        });
      };

      try {
        const leftText = leftPageItems.length > 0 ? (await getPageTextAndItems(leftPage)).text : "";
        await readPage(leftText, leftPageItems, leftPage - 1);

        if (!isCoverPage && rightPage && rightPageItems.length > 0) {
          const rightText = (await getPageTextAndItems(rightPage)).text;
          await readPage(rightText, rightPageItems, rightPage - 1);
        }

        console.log(`Finished reading spread ${isCoverPage ? 'Cover page' : `Pages ${leftPage}-${rightPage}`}`);
        handleReadingComplete();
      } catch (error) {
        console.error("Error during auto-reading:", error);
        handleReadingComplete();
      }
    }, 500);

    return () => clearTimeout(readingTimeout);
  }, [
    isAutoReading,
    documentInfo,
    speaking,
    isLoading,
    getSpreadTextAndItems,
    getPageTextAndItems,
    speak,
    handleReadingComplete,
  ]);

  useEffect(() => {
    if (!documentInfo) return;
    const isCoverPage = documentInfo.currentPage === 1;
    const newLeftPage = isCoverPage ? 1 : documentInfo.currentPage % 2 === 0 ? documentInfo.currentPage : documentInfo.currentPage - 1;
    const newRightPage = isCoverPage ? null : (newLeftPage + 1 <= (documentInfo?.totalPages || 0) ? newLeftPage + 1 : null);
    setLeftPage(newLeftPage);
    setRightPage(newRightPage);
    (async () => {
      const { text, textItems } = await getSpreadTextAndItems(newLeftPage, newRightPage);
      setSpreadTextItems(textItems);
      setCurrentHighlightedText(text);
    })();
  }, [documentInfo?.currentPage, documentInfo?.totalPages, getSpreadTextAndItems]);

  const handlePlay = () => {
    if (speaking) {
      pause();
    } else if (!isAutoReading) {
      // Reset highlights when starting new reading session
      clearHighlights();
      setCurrentHighlight(null);
      setCurrentHighlightedWord(null);
      setReadWords(new Set());
    }
    setIsAutoReading(true);
    setIsStoppedAndReady(false);
  };

  const handlePause = () => {
    pause();
  };

  const handleResume = () => {
    resume();
  };

  const handleStop = () => {
    stopSpeech();
    setIsAutoReading(false);
    clearHighlights();
    setCurrentHighlight(null);
    setCurrentHighlightedWord(null);
    setIsStoppedAndReady(true);
  };

  const handleNext = async () => {
    if (!documentInfo) return;
    stopSpeech();
    setIsAutoReading(false);
    clearHighlights();
    setCurrentHighlight(null);
    setCurrentHighlightedWord(null);
    setIsStoppedAndReady(false);

    const currentPage = documentInfo.currentPage;
    const isCoverPage = currentPage === 1;

    let nextPage;
    if (isCoverPage) {
      nextPage = 2;
    } else {
      nextPage = currentPage % 2 === 0 ? currentPage + 2 : currentPage + 1;
    }

    if (nextPage <= documentInfo.totalPages) {
      console.log(`Navigating to next spread starting with page ${nextPage}`);
      await setCurrentPage(nextPage);
      setIsAutoReading(true);
    } else {
      console.log("Reached end of document");
    }
  };

  const handlePrevious = async () => {
    if (!documentInfo) return;
    stopSpeech();
    setIsAutoReading(false);
    clearHighlights();
    setCurrentHighlight(null);
    setCurrentHighlightedWord(null);
    setIsStoppedAndReady(false);

    const currentPage = documentInfo.currentPage;

    let prevPage;
    if (currentPage <= 3) {
      prevPage = 1;
    } else {
      prevPage = currentPage % 2 === 0 ? currentPage - 2 : currentPage - 3;
    }

    if (prevPage >= 1) {
      console.log(`Navigating to previous spread starting with page ${prevPage}`);
      await setCurrentPage(prevPage);
      setIsAutoReading(true);
    }
  };

  return (
    <Box 
      sx={{ 
        minHeight: "100vh",
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        backgroundColor: "white",
        overflow: "hidden",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      }}
    >
      <Header onHistoryClick={() => {}} />
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          p: { xs: 1, sm: 2 },
          height: "calc(100vh - 64px)", // Subtract header height
          position: "relative"
        }}
      >
        <Box 
          sx={{ 
            py: 1,
            px: { xs: 1, sm: 2 },
            display: "flex", 
            justifyContent: "center",
            backgroundColor: "rgba(0,0,0, 0.1)",
            width: "30%",
            borderRadius: 8,
            mb: 2,
            position: "sticky",
            top: 0,
            zIndex: 1,
            mx: "auto" 
          }}
        >
          <AudioControls
            playing={speaking}
            paused={paused}
            onPlay={handlePlay}
            onPause={handlePause}
            onResume={handleResume}
            onStop={handleStop}
            onNext={handleNext}
            onPrevious={handlePrevious}
            voiceOptions={speechOptions}
            setVoiceOptions={setSpeechOptions}
            availableVoices={voices}
            onHighlightAndRead={() => setHighlightMode(!highlightMode)}
            highlightMode={highlightMode}
          />
        </Box>
        <Box
          sx={{
            flex: 1,
            position: "relative",
            backgroundColor: "rgba(255, 255, 255, 0.05)",
            borderRadius: 2,
            overflow: "auto",
            height: "calc(100% - 80px)" // Subtract audio controls height + margin
          }}
          ref={containerRef}
        >
          <PDFViewer
            document={documentInfo}
            highlights={highlights}
            onPageChange={setCurrentPage}
            onTextSelection={handleTextSelection}
            currentHighlight={currentHighlight}
            pageHeight={documentInfo?.pageHeight || 0}
            isReading={isAutoReading || speaking}
            textItems={spreadTextItems}
            currentHighlightedWord={currentHighlightedWord}
            onWordClick={handleWordClick}
            onSentenceClick={handleSentenceClick}
            onParagraphClick={handleParagraphClick}
            onWordHover={handleWordHover}
            onSentenceHover={handleSentenceHover}
            onParagraphHover={handleParagraphHover}
            speaking={speaking}
            paused={paused}
            readWords={readWords}
            highlightMode={highlightMode}
            leftPage={leftPage}
            rightPage={rightPage}
          />
        </Box>
      </Box>
      <Footer />
    </Box>
  );
};

export default HomePage;
