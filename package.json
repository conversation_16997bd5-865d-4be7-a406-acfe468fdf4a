{"name": "tsls-handbook-pdftoaudio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.17.1", "framer-motion": "^12.18.1", "lucide-react": "^0.344.0", "pdfjs-dist": "^3.11.174", "react": "^18.3.1", "react-dom": "^18.3.1", "react-pageflip": "^2.0.3", "react-pdf": "^7.7.3", "react-router-dom": "^6.15.0", "tesseract.js": "^6.0.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/pdfjs-dist": "^2.10.377", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "worker-loader": "^3.0.8"}}